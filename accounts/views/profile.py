from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from roles.permissions import HasProfileAccess
from roles.serializers.profile import UserProfileSerializer

class ProfileViewSet(viewsets.ModelViewSet):
    serializer_class = UserProfileSerializer
    permission_classes = [HasProfileAccess]
    
    def get_queryset(self):
        # Only return the user's own profile
        return [self.request.user]
        
    @action(detail=True, methods=['get'])
    def full_profile(self, request, pk=None):
        """
        Get full profile information including sensitive data.
        Requires either:
        1. User accessing their own profile
        2. Valid access token with view_profile permission
        """
        user = self.get_object()
        serializer = self.get_serializer(user)
        return Response(serializer.data)