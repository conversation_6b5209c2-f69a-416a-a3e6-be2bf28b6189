from rest_framework import serializers
from ..models import Profile, Education, ResearchPaper, Award, YouTubeVideo, PracticeLocation, CredentialDocument, CustomInformation
from accounts.serializer import Base64<PERSON><PERSON><PERSON>ield
from django.core.validators import RegexValidator
from django.contrib.auth import get_user_model
from enterprise.models import Enterprise, EnterpriseMember

CustomUser = get_user_model()

class ProfileSerializer(serializers.ModelSerializer):
    full_name = serializers.ReadOnlyField()
    profile_picture = Base64ImageField(max_length=None, use_url=True)

    class Meta:
        model = Profile
        fields = ["bio", "profile_picture", "credentials"]
        read_only_fields = ["user"]
        
class ProfileToggleSerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = ['is_public_profile']        

class EducationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Education
        fields = ['id', 'institution', 'degree', 'field_of_study', 'start_date', 'end_date', 'description']

class ResearchPaperSerializer(serializers.ModelSerializer):
    class Meta:
        model = ResearchPaper
        fields = ['id', 'title', 'publication_date', 'journal', 'url']
        
class AwardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Award
        fields = ['id', 'title', 'year', 'description']

class YouTubeVideoSerializer(serializers.ModelSerializer):
    class Meta:
        model = YouTubeVideo
        fields = ['id', 'title', 'url', 'description']

class PracticeLocationSerializer(serializers.ModelSerializer):
    class Meta:
        model = PracticeLocation
        fields = ['id', 'name', 'address', 'phone', 'website']        
       
class CustomInformationSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomInformation
        fields = ['id', 'title', 'description']        

class CredentialDocumentSerializer(serializers.ModelSerializer):
    document = serializers.FileField(max_length=None, use_url=True, required=False)

    class Meta:
        model = CredentialDocument
        fields = ['id', 'document', 'uploaded_at']
        read_only_fields = ['uploaded_at']

class UserProfileSerializer(serializers.ModelSerializer):
    user_id = serializers.IntegerField(source='user.id', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    second_email = serializers.EmailField(source='user.second_email', read_only=True)
    username = serializers.CharField(
        # source='user.username', 
        required=False, 
        allow_blank=True,
        validators=[
            RegexValidator(
                regex='^[a-zA-Z0-9]+$',
                message=('Username must be alphanumeric.'),
                code='invalid_username'
            ),
        ]
    )  
    clinic_id = serializers.CharField(source='user.clinic.id', required=False, allow_null=True)
    enterprise_id = serializers.CharField(source='user.enterprise.id', required=False, allow_null=True)
    enterprise_logo = serializers.CharField(source='user.enterprise.logo', required=False, allow_null=True, default=None)
    enterprise_website = serializers.CharField(source='user.enterprise.website', required=False, allow_null=True, default=None)
    cuid = serializers.CharField(source='user.clinic.unique_identifier', required=False, allow_null=True)
    euid = serializers.CharField(source='user.enterprise.euid', required=False, allow_null=True)
    first_name = serializers.CharField(source='user.first_name', required=False, allow_blank=True)
    last_name = serializers.CharField(source='user.last_name', required=False, allow_blank=True)
    role = serializers.CharField(source='user.role.name', read_only=True)
    profile_picture = serializers.ImageField(required=False, allow_null=True)
    private_profile_picture = serializers.ImageField(required=False, allow_null=True)
    background_image = serializers.ImageField(required=False, allow_null=True)
    gender = serializers.CharField(required=False, allow_blank  =True)
    locations = serializers.CharField(required=False, allow_blank=True)
    dob = serializers.CharField(required=False, allow_blank=True)
    genome_tested = serializers.BooleanField(required=False, allow_null=True)
    language = serializers.CharField(required=False, allow_blank=True)
    blood_group = serializers.CharField(required=False, allow_blank=True)
    digital_blood = serializers.CharField(required=False, allow_blank=True)
    address = serializers.CharField(required=False, allow_blank=True)
    city = serializers.CharField(required=False, allow_blank=True)
    state = serializers.CharField(required=False, allow_blank=True)
    country = serializers.CharField(required=False, allow_blank=True)
    zipcode = serializers.CharField(required=False, allow_blank=True)
    mobile = serializers.CharField(required=False, allow_blank=True)
    homephone = serializers.CharField(required=False, allow_blank=True)
    is_email_verified = serializers.BooleanField(source='user.is_email_verified', read_only=True)
    is_credentials_verified = serializers.BooleanField(read_only=True)
    research_papers = ResearchPaperSerializer(many=True, required=False, source='research_papers_list')
    awards = AwardSerializer(many=True, required=False, source='awards_list')
    youtube_videos = YouTubeVideoSerializer(many=True, required=False, source='youtube_videos_list')
    practice_locations = PracticeLocationSerializer(many=True, required=False)
    education = EducationSerializer(many=True, required=False, source='education_list')
    custom_information = CustomInformationSerializer(many=True, required=False)
    profile_picture_delete = serializers.BooleanField(required=False)
    is_public_profile = serializers.BooleanField(required=False)
    
    phone_number = serializers.CharField(source='user.phone_number', required=False, allow_blank=True)
    is_phone_verified = serializers.BooleanField(source='user.is_phone_verified', required=False)

    middle_name = serializers.CharField(source='user.middle_name', required=False, allow_blank=True)
    title = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    professional_title = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    paid_for_verification = serializers.BooleanField(source='user.paid_for_verification', required=False)


    class Meta:
        model = Profile
        fields = [
            'user_id', 'email', 'second_email', 'username', 'clinic_id', 'enterprise_id', 'cuid','euid', 'first_name', 'last_name', 'role', 'bio','dob', 'genome_tested', 'language', 'profile_picture',
            'private_profile_picture', 'background_image', 'gender', 'blood_group', 'digital_blood', 'address', 'city', 'state', 'country', 'zipcode', 'mobile', 'homephone',
            'is_email_verified', 'is_credentials_verified', 'locations', 'title', 'professional_title', 'education', 'about_me',
            'practices', 'affiliations', 'research_papers', 'awards', 'is_public_profile', 'youtube_videos',
            'speciality', 'practice_locations', 'education', 'custom_information',
            'profile_picture_delete','show_education', 'show_research_papers', 'show_awards',
            'show_youtube_videos', 'show_practice_locations',
            'show_credential_documents', 'show_custom_information',
            'phone_number', 'is_phone_verified',
            'middle_name',
            'paid_for_verification',
            'enterprise_logo', 'enterprise_website'
        ]
        extra_kwargs = {field: {'required': False, 'allow_null': True} for field in fields if field != 'profile_picture_delete' and field != 'private_profile_picture'}
        read_only_fields = ['user_id','email', 'phone_number', 'is_phone_verified']
    def validate_username(self, value):
        if value and not value.isalnum():
            raise serializers.ValidationError("Username must be alphanumeric.")
        return value


    def update(self, instance, validated_data):
        
        user_data = validated_data.pop('user', {})
        private_profile_picture = validated_data.pop('private_profile_picture', None)
        profile_picture_delete = validated_data.pop('profile_picture_delete', False)
            
        user = instance.user    
        for attr, value in user_data.items():
            if attr == 'username':
                if value and not value.isalnum():
                    raise serializers.ValidationError({"username": "Username must be alphanumeric."})
            setattr(user, attr, value)
        user.save()
        
        if 'username' in validated_data:
            instance.username = validated_data.pop('username')

        nested_fields = {
            'research_papers_list': ResearchPaperSerializer,
            'awards_list': AwardSerializer,
            'youtube_videos_list': YouTubeVideoSerializer,
            'practice_locations': PracticeLocationSerializer,
            'education_list': EducationSerializer
        }

        for field, serializer_class in nested_fields.items():
            if field in validated_data:
                self.update_nested_data(instance, field, validated_data.pop(field), serializer_class)

        custom_information_data = validated_data.pop('custom_information', None)
        if custom_information_data is not None:
            instance.custom_information.all().delete()
            for item in custom_information_data:
                CustomInformation.objects.create(profile=instance, **item)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)

        instance.save()
        return instance

    def update_nested_data(self, instance, field_name, data, serializer_class):
        related_manager = getattr(instance, field_name)
        related_manager.all().delete()
        for item in data:
            serializer = serializer_class(data=item)
            if serializer.is_valid():
                serializer.save(profile=instance)

    def to_representation(self, instance):
        data = super().to_representation(instance)
        user = instance.user
        # Ưu tiên lấy từ user.enterprise nếu có
        enterprise = getattr(user, 'enterprise', None)
        if not enterprise:
            enterprise = Enterprise.objects.filter(owner=user).first()
            if not enterprise:
                membership = EnterpriseMember.objects.filter(user=user, is_active=True).select_related('enterprise').first()
                if membership:
                    enterprise = membership.enterprise
                    data['enterprise_member_role'] = membership.role
                else:
                    data['enterprise_member_role'] = None
            else:
                # Nếu user là owner
                data['enterprise_member_role'] = 'owner'
        else:
            # Nếu user là owner
            if enterprise.owner == user:
                data['enterprise_member_role'] = 'owner'
            else:
                # Nếu user là member nhưng không phải owner
                membership = EnterpriseMember.objects.filter(user=user, enterprise=enterprise, is_active=True).first()
                data['enterprise_member_role'] = membership.role if membership else None
        data['euid'] = enterprise.euid if enterprise else None
        return data

class UserProfileAdminSerializer(UserProfileSerializer):
    is_credentials_verified = serializers.BooleanField(required=False)

    class Meta(UserProfileSerializer.Meta):
        fields = UserProfileSerializer.Meta.fields + ['is_credentials_verified']

    def update(self, instance, validated_data):
        is_credentials_verified = validated_data.pop('is_credentials_verified', None)
        
        # Update other fields
        instance = super().update(instance, validated_data)

        # Update is_credentials_verified if provided
        if is_credentials_verified is not None:
            instance.is_credentials_verified = is_credentials_verified
            instance.save()

        return instance    
    
class ProfileFieldsVisibilitySerializer(serializers.ModelSerializer):
    class Meta:
        model = Profile
        fields = [
            'show_education', 'show_research_papers', 'show_awards',
            'show_youtube_videos', 'show_practice_locations',
            'show_credential_documents', 'show_custom_information'
        ] 