import logging
from rest_framework import viewsets, status, serializers
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import action
from django.utils import timezone
from django.db import models
from datetime import datetime
from appointments.models import Appointment, DoctorA<PERSON>ilability, DoctorAvailabilityOverride, AppointmentAttachment, DoctorConsultationProfile
from appointments.api.serializers import AppointmentCreateSerializer, AppointmentUpdateSerializer, AppointmentAttachmentSerializer
from appointments.tasks import sync_appointment_to_google_calendar, send_appointment_reminder
from appointments.services.google_meet import create_meet_link
from appointments.services.email_service import AppointmentEmailService
from accounts.models import CustomUser
from clinic.models import ClinicDoctor
import stripe

logger = logging.getLogger(__name__)

def is_doctor(user):
    """Check if user is a doctor by looking up in ClinicDoctor table"""
    return ClinicDoctor.objects.filter(doctor=user).exists()

def is_patient(user):
    """Check if user is a patient by looking up appointments"""
    return Appointment.objects.filter(patient=user).exists()

class AppointmentViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = AppointmentCreateSerializer
    queryset = Appointment.objects.all()

    def get_serializer_class(self):
        if self.action in ['create']:
            return AppointmentCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return AppointmentUpdateSerializer
        return AppointmentCreateSerializer

    def get_permissions(self):
        if self.action == 'create':
            return [AllowAny()]
        return [IsAuthenticated()]

  
    def get_queryset(self):
        print("Entering get_queryset method print")
        logger.info("Entering get_queryset method")

        # Get query parameters
        role = self.request.query_params.get('role')
        status = self.request.query_params.get('status')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        logger.info(f"Query parameters - role: {role}, status: {status}, start_date: {start_date}, end_date: {end_date}")

        # Base queryset based on role
        logger.info(f"Building queryset for user 2: {self.request.user}")
        if role == 'patient':
            logger.info("Filtering for patient role")
            queryset = Appointment.objects.filter(
                models.Q(creator=self.request.user, appointment_type='manual') |
                models.Q(patient=self.request.user, appointment_type='booking')
            )
        elif role == 'doctor':
            logger.info("Filtering for doctor role")
            queryset = Appointment.objects.filter(
                models.Q(creator=self.request.user, appointment_type='manual') |
                models.Q(doctor=self.request.user, appointment_type='booking')
            )
        else:
            logger.info("No specific role specified, applying default filtering")
            # If no role specified, show:
            # - manual appointments where user is creator
            # - booking appointments where user is either patient or doctor
            queryset = Appointment.objects.filter(
                models.Q(creator=self.request.user, appointment_type='manual') |
                models.Q(
                    appointment_type='booking',
                    patient=self.request.user
                ) | models.Q(
                    appointment_type='booking',
                    doctor=self.request.user
                )
            )
        logger.info(f"Initial queryset count: {queryset.count()}")

        # Apply filters
        if status:
            logger.info(f"Applying status filter: {status}")
            queryset = queryset.filter(status=status)
            logger.info(f"Queryset count after status filter: {queryset.count()}")

        if start_date:
            try:
                # Convert to datetime with start of day (00:00:00)
                start_datetime = timezone.make_aware(
                    datetime.combine(
                        datetime.strptime(start_date, '%Y-%m-%d').date(),
                        datetime.min.time()
                    )
                )
                logger.info(f"Applying start_date filter: {start_datetime}")
                queryset = queryset.filter(start_time__gte=start_datetime)
                logger.info(f"Queryset count after start_date filter: {queryset.count()}")
            except ValueError:
                logger.warning(f"Invalid start_date format: {start_date}")
                pass

        if end_date:
            try:
                # Convert to datetime with end of day (23:59:59)
                end_datetime = timezone.make_aware(
                    datetime.combine(
                        datetime.strptime(end_date, '%Y-%m-%d').date(),
                        datetime.max.time()
                    )
                )
                logger.info(f"Applying end_date filter: {end_datetime}")
                queryset = queryset.filter(end_time__lte=end_datetime)
                logger.info(f"Queryset count after end_date filter: {queryset.count()}")
            except ValueError:
                logger.warning(f"Invalid end_date format: {end_date}")
                pass

        # Order and remove duplicates
        logger.info("Ordering queryset by start_time and applying distinct")
        queryset = queryset.order_by('start_time').distinct()
        logger.info(f"Final queryset count: {queryset.count()}")

        return queryset

    def create(self, request, *args, **kwargs):
        """Create a new appointment"""
        logger.info("Entering create appointment method")
        try:
            # Get the user from the request
            user = request.user
            logger.info(f"Processing request for user: {user}")

            # Get the appointment type from the request data
            appointment_type = request.data.get('appointment_type', 'manual')
            logger.info(f"Appointment type: {appointment_type}")
            
            # Prepare the appointment data
            appointment_data = {
                'title': request.data.get('title', ''),
                'appointment_type': appointment_type,
                'start_time': request.data.get('start_time'),
                'end_time': request.data.get('end_time'),
                'status': 'pending',
                'notes': request.data.get('notes', ''),
                'location': request.data.get('location', ''),
                'is_all_day': request.data.get('is_all_day', False),
                'mode': request.data.get('mode', 'in_person'),
                'file_ids': request.data.get('file_ids', []),
            }
            logger.debug(f"Initial appointment data: {appointment_data}")

            # For booking type appointments, add doctor
            if appointment_type == 'booking':
                doctor_id = request.data.get('doctor_id')
                logger.info(f"Processing booking appointment for doctor_id: {doctor_id}")
                
                if not doctor_id:
                    logger.error("Doctor ID missing for booking appointment")
                    return Response({
                        'error': 'Doctor ID required',
                        'detail': 'Doctor ID is required for booking type appointments'
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Add doctor to appointment data
                appointment_data.update({
                    'doctor_id': doctor_id,
                })

                # Add clinic and enterprise if provided
                clinic_id = request.data.get('clinic_id')
                enterprise_id = request.data.get('enterprise_id')
                
                if clinic_id:
                    logger.debug(f"Adding clinic_id: {clinic_id}")
                    appointment_data['clinic_id'] = clinic_id

                if enterprise_id:
                    logger.debug(f"Adding enterprise_id: {enterprise_id}")
                    appointment_data['enterprise_id'] = enterprise_id

                # Add insurance and direct_payment if provided
                insurance = request.data.get('insurance', False)
                direct_payment = request.data.get('direct_payment', False)
                appointment_data['insurance'] = insurance
                appointment_data['direct_payment'] = direct_payment

            try:
                # Create the appointment
                logger.info("Initializing serializer with data and request context")
                serializer = self.get_serializer(data=appointment_data, context={'request': request})
                logger.info("Serializer initialized, calling is_valid()")
                serializer.is_valid(raise_exception=True)
                logger.info("Serializer validation passed, saving appointment")
                appointment = serializer.save()
                logger.info(f"Successfully created appointment with ID: {appointment.id}")
            except serializers.ValidationError as ve:
                logger.error(f"Validation error creating appointment: {ve.detail}")
                raise
            except Exception as e:
                logger.error(f"Unexpected error creating appointment: {str(e)}")
                raise

            # Handle file attachments if provided
            # file_ids = request.data.get('file_ids', [])
            # if file_ids:
            #     try:
            #         logger.info(f"Processing {len(file_ids)} file attachments")
            #         appointment.attachments.set(file_ids)
            #     except Exception as e:
            #         logger.error(f"Error setting file attachments: {str(e)}")
            #         # Continue execution as attachments are not critical

            # Prepare response data with payment information
            response_data = serializer.data
            response_data['payment_required'] = appointment.is_payment_required()

            # If payment is required, include payment details
            if appointment.is_payment_required():
                response_data['payment_info'] = {
                    'direct_payment': appointment.direct_payment,
                    'payment_status': appointment.payment_status,
                    'consultation_fee': appointment.consultation_fee,
                    'consultation_fee_usd': appointment.get_consultation_fee_usd()
                }

            return Response(response_data, status=status.HTTP_201_CREATED)

        except serializers.ValidationError as ve:
            logger.error(f"Validation error in create appointment: {ve.detail}")
            return Response({
                'error': 'Validation error',
                'detail': ve.detail
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"Unexpected error in create appointment: {str(e)}", exc_info=True)
            return Response({
                'error': 'Failed to create appointment',
                'detail': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

    def update(self, request, pk=None):
        """
        Update an existing appointment with different permissions for patients and doctors.
        - For manual appointments: creator has full control
        - For booking appointments: 
            - doctors can update status and certain fields
            - patients can only cancel with reason
        """
        try:
            appointment = self.get_object()
            is_doctor = appointment.doctor == request.user
            is_patient = appointment.patient == request.user
            is_creator = appointment.creator == request.user

            # Determine allowed fields based on user role
            if appointment.appointment_type == 'manual':
                # For manual appointments, only creator can update
                if not is_creator:
                    return Response({
                        'error': 'Permission denied',
                        'detail': 'Only the creator can update manual appointments'
                    }, status=status.HTTP_403_FORBIDDEN)
                allowed_fields = {'title', 'start_time', 'end_time', 'notes', 'location', 'mode'}
            else:  # booking type
                if is_doctor:
                    # Doctors can update these fields
                    allowed_fields = {
                        'status', 'notes', 'location', 'mode',
                        'meeting_link', 'title', 'cancellation_reason',
                        'insurance', 'direct_payment'
                    }
                    logger.info(f"Doctor updating appointment. Allowed fields: {allowed_fields}")

                    # Validate payment status before confirming appointment
                    if request.data.get('status') == 'confirmed':
                        if not appointment.can_be_confirmed_by_doctor():
                            return Response({
                                'error': 'Payment required',
                                'detail': 'Appointment cannot be confirmed until payment is completed',
                                'payment_status': appointment.payment_status,
                                'payment_required': appointment.is_payment_required()
                            }, status=status.HTTP_400_BAD_REQUEST)

                    # If doctor is accepting the appointment and it's a video call, create meet link
                    if request.data.get('status') == 'confirmed' and appointment.mode == 'video_call' and not appointment.meeting_link:
                        try:
                            meet_data = create_meet_link(appointment)
                            if meet_data:
                                request.data['meeting_link'] = meet_data['meet_link']
                                request.data['google_event_id'] = meet_data['event_id']
                                logger.info(f"Created Meet link for appointment {appointment.id}")
                            else:
                                logger.warning(f"Failed to create Meet link for appointment {appointment.id}")
                        except Exception as e:
                            logger.error(f"Error creating Meet link for appointment {appointment.id}: {str(e)}")
                    
                elif is_patient:
                    # Patients can only cancel and update notes
                    allowed_fields = {'status', 'notes'}
                    logger.info(f"Patient updating appointment. Allowed fields: {allowed_fields}")
                    
                    # Only allow cancellation
                    if 'status' in request.data:
                        logger.info(f"Patient attempting status change to: {request.data['status']}")
                        if request.data['status'] != 'canceled':
                            logger.warning(f"Patient attempted invalid status change to {request.data['status']}")
                            return Response({
                                'error': 'Permission denied',
                                'detail': 'Patients can only cancel appointments'
                            }, status=status.HTTP_400_BAD_REQUEST)
                else:
                    logger.warning(f"Invalid user role detected. User ID: {request.user.id}")
                    return Response({
                        'error': 'Permission denied',
                        'detail': 'Invalid user role'
                    }, status=status.HTTP_403_FORBIDDEN)

            update_data = {
                k: v for k, v in request.data.items() 
                if k in allowed_fields
            }

            logger.info(f"Original request data: {request.data}")
            logger.info(f"Filtered update data: {update_data}")

            # Check if status is changing from 'pending' to 'confirmed'
            status_changing_to_confirmed = (
                'status' in update_data and
                update_data['status'] == 'confirmed' and
                appointment.status == 'pending'
            )

            serializer = AppointmentUpdateSerializer(appointment, data=update_data, partial=True)
            if serializer.is_valid():
                serializer.save()

                # Send confirmation emails if status changed to confirmed
                if status_changing_to_confirmed and appointment.appointment_type == 'booking':
                    try:
                        logger.info(f"Sending confirmation emails for appointment {appointment.id}")
                        email_results = AppointmentEmailService.send_appointment_confirmation_emails(appointment)

                        # Log email results
                        if email_results['patient_email_sent']:
                            logger.info(f"Patient confirmation email sent successfully for appointment {appointment.id}")
                        if email_results['doctor_email_sent']:
                            logger.info(f"Doctor confirmation email sent successfully for appointment {appointment.id}")

                        # Log any errors but don't fail the request
                        if email_results['errors']:
                            for error in email_results['errors']:
                                logger.warning(f"Email notification error for appointment {appointment.id}: {error}")

                    except Exception as e:
                        # Log email errors but don't fail the appointment update
                        logger.error(f"Failed to send confirmation emails for appointment {appointment.id}: {str(e)}")

                return Response(serializer.data)
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error updating appointment: {str(e)}")
            return Response({
                'error': 'Failed to update appointment',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def destroy(self, request, pk=None):
        """
        Delete an appointment.
        Only creator or doctor can delete the appointment.
        """
        try:
            appointment = Appointment.objects.get(pk=pk)

            # If it's a booking type appointment, check if it's already confirmed
            if appointment.appointment_type == 'booking' and appointment.status == 'confirmed':
                return Response(
                    {'error': 'Cannot delete a confirmed booking appointment'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )

            appointment.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except Appointment.DoesNotExist:
            return Response(
                {'error': 'Appointment not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in deleting appointment: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def upcoming(self, request):
        """
        Get the list of upcoming appointments (manual or booking).
        """
        try:
            now = timezone.now()
            # Get all upcoming appointments related to the user
            queryset = Appointment.objects.filter(
                models.Q(creator=request.user) |
                models.Q(patient=request.user) |
                models.Q(doctor=request.user),
                start_time__gte=now,
                status__in=['pending', 'confirmed']
            ).distinct()
            serializer = self.serializer_class(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in listing upcoming appointments: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            
    @action(detail=True, methods=['get'])
    def attachments(self, request, pk=None):
        """
        Get all attachments for a specific appointment.
        """
        try:
            appointment = self.get_object()
            attachments = AppointmentAttachment.objects.filter(appointment=appointment)
            serializer = AppointmentAttachmentSerializer(attachments, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error retrieving appointment attachments: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'])
    def process_payment(self, request, pk=None):
        """
        Process payment for a specific appointment.
        For patients to pay for telemedicine consultations.
        
        Expected payload:
        {
            "payment_method_id": "pm_xxx" (optional - for saved payment methods)
        }
        """
        try:
            appointment = self.get_object()
            
            # Validate appointment belongs to current user as patient
            if appointment.patient != request.user:
                return Response({
                    'error': 'Permission denied',
                    'detail': 'You can only process payment for your own appointments'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Validate appointment requires payment
            if not appointment.is_payment_required():
                return Response({
                    'error': 'Payment not required',
                    'detail': 'This appointment does not require payment'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if already paid
            if appointment.is_paid():
                return Response({
                    'error': 'Already paid',
                    'detail': 'This appointment has already been paid for'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if payment transfer already exists
            if appointment.payment_transfer:
                # Return existing checkout URL if session is still valid
                from billing.services.appointment_payment_service import AppointmentPaymentService
                appointment_service = AppointmentPaymentService()
                
                try:
                    # Check if we have a checkout session
                    if hasattr(appointment.payment_transfer, 'stripe_checkout_session_id') and appointment.payment_transfer.stripe_checkout_session_id:
                        
                        session = stripe.checkout.Session.retrieve(appointment.payment_transfer.stripe_checkout_session_id)
                        
                        # If session is still open, return the existing URL
                        if session.status == 'open':
                            return Response({
                                'checkout_url': session.url,
                                'amount': appointment.consultation_fee,
                                'amount_usd': appointment.get_consultation_fee_usd(),
                                'doctor_name': appointment.doctor.get_full_name() if appointment.doctor else 'Unknown',
                                'appointment_id': str(appointment.id),
                                'status': 'existing_checkout_session'
                            })
                        
                except Exception as e:
                    logger.warning(f"Could not retrieve existing checkout session: {str(e)}")
                    # Continue to create new checkout session
            
            # Get doctor's consultation profile for fee
            try:
                consultation_profile = DoctorConsultationProfile.objects.get(user=appointment.doctor)
                consultation_fee = consultation_profile.consultation_fee
            except DoctorConsultationProfile.DoesNotExist:
                return Response({
                    'error': 'Doctor consultation profile not found',
                    'detail': 'Doctor has not set up consultation profile'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Create payment transfer
            from billing.services.appointment_payment_service import AppointmentPaymentService
            appointment_service = AppointmentPaymentService()
            
            transfer, checkout_url = appointment_service.create_appointment_checkout_session(
                patient=request.user,
                doctor=appointment.doctor,
                appointment=appointment,
                consultation_fee=consultation_fee
            )
            
            logger.info(f"Created checkout session for appointment {appointment.id}")
            
            return Response({
                'checkout_url': checkout_url,
                'amount': consultation_fee,
                'amount_usd': consultation_profile.get_consultation_fee_usd(),
                'doctor_name': appointment.doctor.get_full_name(),
                'appointment_id': str(appointment.id),
                'consultation_duration': consultation_profile.consultation_duration,
                'status': 'checkout_session_created'
            }, status=status.HTTP_201_CREATED)
            
        except Exception as e:
            logger.error(f"Error processing appointment payment: {str(e)}")
            return Response({
                'error': 'Failed to process payment',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def create_telemedicine_appointment(self, request):
        """
        Create telemedicine appointment with payment processing
        Streamlined endpoint for: appointment_type='booking' + mode='video_call' + direct_payment=True
        """
        try:
            data = request.data
            doctor_id = data.get('doctor_id')

            if not doctor_id:
                return Response({
                    'error': 'doctor_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate doctor exists and has consultation profile
            try:
                doctor = CustomUser.objects.get(id=doctor_id, role__name='doctor')
            except CustomUser.DoesNotExist:
                return Response({
                    'error': 'Doctor not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Check if doctor has consultation profile
            try:
                consultation_profile = DoctorConsultationProfile.objects.get(user=doctor)
                if not consultation_profile.is_available_for_telemedicine():
                    return Response({
                        'error': 'Doctor is not available for telemedicine consultations'
                    }, status=status.HTTP_400_BAD_REQUEST)
            except DoctorConsultationProfile.DoesNotExist:
                return Response({
                    'error': 'Doctor has not set up consultation profile'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Prepare appointment data for telemedicine
            appointment_data = {
                'appointment_type': 'booking',
                'mode': 'video_call',
                'direct_payment': True,
                'doctor_id': doctor_id,
                'start_time': data.get('start_time'),
                'end_time': data.get('end_time'),
                'notes': data.get('notes', ''),
                'title': data.get('title', f"Telemedicine consultation with {doctor.get_full_name()}")
            }

            # Create appointment
            serializer = self.get_serializer(data=appointment_data, context={'request': request})
            serializer.is_valid(raise_exception=True)
            appointment = serializer.save()

            # Create peer-to-peer payment transfer
            from billing.services.appointment_payment_service import AppointmentPaymentService
            appointment_service = AppointmentPaymentService()

            transfer, checkout_url = appointment_service.create_appointment_checkout_session(
                patient=request.user,
                doctor=doctor,
                appointment=appointment,
                consultation_fee=consultation_profile.consultation_fee
            )

            logger.info(f"Created telemedicine appointment {appointment.id} with checkout session")

            return Response({
                'appointment': AppointmentCreateSerializer(appointment).data,
                'payment': {
                    'checkout_url': checkout_url,
                    'amount': consultation_profile.consultation_fee,
                    'amount_usd': consultation_profile.get_consultation_fee_usd(),
                    'doctor_name': doctor.get_full_name(),
                    'consultation_duration': consultation_profile.consultation_duration
                }
            }, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(f"Error creating telemedicine appointment: {str(e)}")
            return Response({
                'error': 'Failed to create telemedicine appointment',
                'detail': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)