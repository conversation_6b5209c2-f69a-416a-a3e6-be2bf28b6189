import logging
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.decorators import action
from django.utils import timezone
from django.db import models
from datetime import datetime, timedelta
from appointments.models import DoctorAvailability, Appointment
from appointments.api.serializers import DoctorAvailabilitySerializer
from appointments.models.override import DoctorAvailabilityOverride
from clinic.models import ClinicD<PERSON>tor
from appointments.constants import MAX_DATE_RANGE_DAYS, DEFAULT_UPCOMING_DAYS

logger = logging.getLogger(__name__)

def is_doctor(user):
    """Check if user is a doctor by looking up in ClinicDoctor table"""
    return ClinicDoctor.objects.filter(doctor=user).exists()

class DoctorAvailabilityViewSet(viewsets.ModelViewSet):
    permission_classes = [IsAuthenticated]
    serializer_class = DoctorAvailabilitySerializer
    queryset = DoctorAvailability.objects.all()

    def get_permissions(self):
        """
        Override get_permissions to allow public access to available_slots
        """
        if self.action == 'available_slots':
            return [AllowAny()]
        return [IsAuthenticated()]

    def get_queryset(self):
        """Override get_queryset to filter based on user role and date range"""
        doctor_id = self.request.query_params.get('doctor_id')
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if doctor_id:
            queryset = DoctorAvailability.objects.filter(doctor_id=doctor_id)
        elif is_doctor(self.request.user):
            queryset = DoctorAvailability.objects.filter(doctor=self.request.user)
        else:
            queryset = DoctorAvailability.objects.filter(is_active=True)
            
        # Filter by date range if provided
        if start_date:
            queryset = queryset.filter(start_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(
                models.Q(end_date__isnull=True) | models.Q(end_date__lte=end_date)
            )
            
        return queryset

    def list(self, request):
        """Get all availabilities (filtered by doctor if user is a doctor)"""
        try:
            doctor_id = request.query_params.get('doctor_id')
            
            if doctor_id:
                # If doctor_id is provided, get availabilities for that doctor
                queryset = DoctorAvailability.objects.filter(doctor_id=doctor_id).order_by('start_date', 'start_time')
            elif is_doctor(request.user):
                # If user is a doctor, get their own availabilities
                queryset = DoctorAvailability.objects.filter(doctor=request.user).order_by('start_date', 'start_time')
            else:
                # For other users, get only records they created
                queryset = DoctorAvailability.objects.filter(doctor=request.user).order_by('start_date', 'start_time')
                
            serializer = self.serializer_class(queryset, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in listing availabilities: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _create_single_availability(self, data):
        """Helper method to create a single availability"""
        serializer = self.serializer_class(data=data)
        if serializer.is_valid():
            availability = serializer.save(doctor=self.request.user)
            return {
                'status': 'created',
                'data': serializer.data,
                'errors': None
            }
        return {
            'status': 'error',
            'data': data,
            'errors': serializer.errors
        }

    def _update_single_availability(self, instance, data):
        """Helper method to update a single availability"""
        serializer = self.serializer_class(
            instance,
            data=data,
            partial=True
        )
        if serializer.is_valid():
            availability = serializer.save()
            return {
                'status': 'updated',
                'data': serializer.data,
                'errors': None
            }
        return {
            'status': 'error',
            'data': data,
            'errors': serializer.errors
        }

    @action(detail=False, methods=['post'])
    def bulk_save(self, request):
        """Bulk create or update availabilities"""
        if not isinstance(request.data, list):
            return Response(
                {'error': 'Data must be an array'},
                status=status.HTTP_400_BAD_REQUEST
            )

        results = []
        for item in request.data:
            availability_id = item.get('id')
            
            try:
                # Check for duplicate availability
                if not availability_id:
                    # For new availabilities, check if a similar one already exists
                    existing = DoctorAvailability.objects.filter(
                        doctor=request.user,
                        start_date=item.get('start_date'),
                        start_time=item.get('start_time'),
                        end_time=item.get('end_time'),
                        recurrence_type=item.get('recurrence_type', 'none'),
                        mode=item.get('mode', 'in_person')  # Add mode to duplicate check
                    )
                    
                    if item.get('clinic'):
                        existing = existing.filter(clinic_id=item.get('clinic'))
                    elif item.get('enterprise'):
                        existing = existing.filter(enterprise_id=item.get('enterprise'))
                    
                    if existing.exists():
                        # Update the existing availability instead of creating a new one
                        instance = existing.first()
                        result = self._update_single_availability(instance, item)
                        results.append(result)
                        continue
                
                if availability_id:
                    # Update existing
                    try:
                        instance = DoctorAvailability.objects.get(
                            id=availability_id,
                            doctor=request.user
                        )
                        result = self._update_single_availability(instance, item)
                    except DoctorAvailability.DoesNotExist:
                        result = {
                            'status': 'error',
                            'data': item,
                            'errors': {'id': ['Availability not found']}
                        }
                else:
                    # Create new
                    result = self._create_single_availability(item)
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"Error processing availability: {str(e)}")
                results.append({
                    'status': 'error',
                    'data': item,
                    'errors': {'detail': str(e)}
                })

        # Check if any operation was successful
        if not any(r['status'] in ['created', 'updated'] for r in results):
            return Response(
                {
                    'error': 'No availabilities were processed successfully',
                    'results': results
                },
                status=status.HTTP_400_BAD_REQUEST
            )

        return Response({
            'message': 'Bulk operation completed',
            'results': results
        })

    def destroy(self, request, pk=None):
        """Delete specific availability"""
        try:
            availability = DoctorAvailability.objects.get(pk=pk)
            availability.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)
        except DoctorAvailability.DoesNotExist:
            return Response(
                {'error': 'Availability not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            logger.error(f"Error in deleting availability: {str(e)}")
            return Response(
                {'error': str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def my_available_slots(self, request):
        """
        Get available slots for the logged-in doctor within a date range.
        
        Query parameters:
        - start_date: Start date in YYYY-MM-DD format (required)
        - end_date: End date in YYYY-MM-DD format (required)
        - view_type: Type of view (day, week, month) - affects grouping
        - include_bookings: Whether to include booking information (default: true)
        - timezone: Timezone for time conversion (e.g., 'Asia/Ho_Chi_Minh')
        """
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        view_type = request.query_params.get('view_type', 'day')
        include_bookings = request.query_params.get('include_bookings', 'true').lower() == 'true'
        timezone_str = request.query_params.get('timezone', 'UTC')

        if not all([start_date, end_date]):
            return Response(
                {'error': 'start_date and end_date are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            # Validate date range
            if start_date > end_date:
                return Response(
                    {'error': 'start_date must be before or equal to end_date'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Limit date range to 90 days for performance
            if (end_date - start_date).days > 90:
                return Response(
                    {'error': 'Date range cannot exceed 90 days'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all availabilities for the doctor
        # For non-recurring availabilities, we need to check if they fall within the date range
        # For recurring availabilities, we need to check if their start_date is before or equal to end_date
        availabilities = DoctorAvailability.objects.filter(
            doctor=request.user,
            is_active=True
        ).filter(
            models.Q(
                # Non-recurring availabilities
                recurrence_type='none',
                start_date__lte=end_date,
                end_date__gte=start_date
            ) |
            models.Q(
                # Recurring availabilities
                ~models.Q(recurrence_type='none'),
                start_date__lte=end_date
            )
        )

        # Get all occurrences
        all_occurrences = []
        for availability in availabilities:
            occurrences = availability.get_occurrences(start_date, end_date)
            for occurrence in occurrences:
                # Add availability metadata to each occurrence
                occurrence['availability_id'] = availability.id
                occurrence['title'] = availability.title or f"{availability.get_recurrence_type_display()} Availability"
                occurrence['clinic_id'] = availability.clinic_id
                occurrence['enterprise_id'] = availability.enterprise_id
                occurrence['recurrence_type'] = availability.recurrence_type
                all_occurrences.append(occurrence)

        # Sort by date and time
        all_occurrences.sort(key=lambda x: (x['date'], x['start_time']))

        # Get booked appointments if requested
        booked_appointments = {}
        if include_bookings:
            from appointments.models import Appointment
            from django.utils import timezone
            
            # Convert dates to timezone-aware datetimes for comparison
            start_datetime = timezone.make_aware(
                datetime.combine(start_date, datetime.min.time())
            )
            end_datetime = timezone.make_aware(
                datetime.combine(end_date, datetime.max.time())
            )
            
            appointments = Appointment.objects.filter(
                doctor=request.user,
                start_time__range=(start_datetime, end_datetime),
                status__in=['pending', 'confirmed']
            ).select_related('patient', 'clinic', 'enterprise')
            
            for appointment in appointments:
                date_str = appointment.start_time.date().strftime('%Y-%m-%d')
                if date_str not in booked_appointments:
                    booked_appointments[date_str] = []
                
                booked_appointments[date_str].append({
                    'id': appointment.id,
                    'start_time': appointment.start_time.strftime('%H:%M'),
                    'end_time': appointment.end_time.strftime('%H:%M'),
                    'status': appointment.status,
                    'patient_id': appointment.patient_id,
                    'patient_name': appointment.patient.get_full_name() if appointment.patient else None,
                    'title': appointment.title,
                    'clinic_id': appointment.clinic_id,
                    'enterprise_id': appointment.enterprise_id,
                    'appointment_type': appointment.appointment_type,
                    'mode': appointment.mode
                })

        # Group by date
        slots_by_date = {}
        for occurrence in all_occurrences:
            date_str = occurrence['date'].strftime('%Y-%m-%d')
            if date_str not in slots_by_date:
                # Initialize date entry with metadata
                slots_by_date[date_str] = {
                    'date': date_str,
                    'day_of_week': occurrence['date'].strftime('%A'),
                    'slots': [],
                    'total_slots': 0,
                    'available_slots': 0,
                    'booked_slots': 0
                }
            
            # Check if this slot is booked
            is_booked = False
            booking_info = None
            
            if include_bookings and date_str in booked_appointments:
                for booking in booked_appointments[date_str]:
                    booking_start = datetime.strptime(booking['start_time'], '%H:%M').time()
                    booking_end = datetime.strptime(booking['end_time'], '%H:%M').time()
                    
                    if (booking_start <= occurrence['end_time'] and 
                        booking_end >= occurrence['start_time']):
                        is_booked = True
                        booking_info = booking
                        break

            # Add slot with booking information
            slot = {
                'start_time': occurrence['start_time'].strftime('%H:%M'),
                'end_time': occurrence['end_time'].strftime('%H:%M'),
                'availability_id': occurrence['availability_id'],
                'title': occurrence['title'],
                'clinic_id': occurrence['clinic_id'],
                'enterprise_id': occurrence['enterprise_id'],
                'recurrence_type': occurrence['recurrence_type'],
                'mode': availability.mode,
                'status': 'booked' if is_booked else 'available',
                'booking_info': booking_info
            }
            
            slots_by_date[date_str]['slots'].append(slot)
            slots_by_date[date_str]['total_slots'] += 1
            
            if is_booked:
                slots_by_date[date_str]['booked_slots'] += 1
            else:
                slots_by_date[date_str]['available_slots'] += 1

        # Group by view_type if needed
        result = {
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'view_type': view_type,
            'timezone': timezone_str,
            'total_days': len(slots_by_date),
            'total_slots': sum(day['total_slots'] for day in slots_by_date.values()),
            'total_available_slots': sum(day['available_slots'] for day in slots_by_date.values()),
            'total_booked_slots': sum(day['booked_slots'] for day in slots_by_date.values()),
            'schedule': []
        }
        
        # Convert dictionary to list and sort by date
        for date_str, day_data in sorted(slots_by_date.items()):
            result['schedule'].append(day_data)
            
        return Response(result)

    @action(detail=False, methods=['get'])
    def get_available_slots(self, request):
        """
        Get available slots for a specific doctor within a date range.
        
        Query parameters:
        - doctor_id: ID of the doctor (required)
        - start_date: Start date in YYYY-MM-DD format (required)
        - end_date: End date in YYYY-MM-DD format (required)
        - mode: Appointment mode (video_call or in_person)
        """
        doctor_id = request.query_params.get('doctor_id')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        mode = request.query_params.get('mode', 'in_person')

        if not all([doctor_id, start_date, end_date]):
            return Response(
                {'error': 'doctor_id, start_date, and end_date are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            
            # Validate date range
            if start_date > end_date:
                return Response(
                    {'error': 'start_date must be before or equal to end_date'},
                    status=status.HTTP_400_BAD_REQUEST
                )
                
            # Limit date range to 90 days for performance
            if (end_date - start_date).days > MAX_DATE_RANGE_DAYS:
                return Response(
                    {'error': f'Date range cannot exceed {MAX_DATE_RANGE_DAYS} days'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get all active availabilities for the doctor
            availabilities = DoctorAvailability.objects.filter(
                doctor_id=doctor_id,
                is_active=True
            ).filter(
                models.Q(
                    # Non-recurring availabilities
                    recurrence_type='none',
                    start_date__gte=start_date,
                    start_date__lte=end_date
                ) |
                models.Q(
                    # Recurring availabilities
                    ~models.Q(recurrence_type='none'),
                    start_date__lte=end_date
                )
            )

            # Filter by mode if provided
            if mode:
                availabilities = availabilities.filter(mode__contains=[mode])

            # Get all appointments for the doctor in the date range
            appointments = Appointment.objects.filter(
                doctor_id=doctor_id,
                start_time__date__gte=start_date,
                end_time__date__lte=end_date,
                status__in=['pending', 'confirmed']
            )

            # Get all overrides for the doctor in the date range
            overrides = DoctorAvailabilityOverride.objects.filter(
                doctor_id=doctor_id,
                date__gte=start_date,
                date__lte=end_date
            )

            # Generate all possible slots
            slots = []
            for availability in availabilities:
                occurrences = availability.get_occurrences(start_date, end_date)
                for occurrence in occurrences:
                    slot_date = occurrence['date']
                    slot_start = occurrence['start_time']
                    slot_end = occurrence['end_time']

                    # Check if there's an override for this date
                    override = overrides.filter(date=slot_date).first()
                    if override and not override.is_available:
                        continue

                    # Check if there's an appointment in this slot
                    slot_start_dt = timezone.make_aware(
                        datetime.combine(slot_date, slot_start)
                    )
                    slot_end_dt = timezone.make_aware(
                        datetime.combine(slot_date, slot_end)
                    )

                    conflicting_appointment = appointments.filter(
                        start_time__lt=slot_end_dt,
                        end_time__gt=slot_start_dt
                    ).exists()

                    if not conflicting_appointment:
                        slots.append({
                            'date': slot_date.isoformat(),
                            'start_time': slot_start.strftime('%H:%M:%S'),
                            'end_time': slot_end.strftime('%H:%M:%S'),
                            'mode': availability.mode  # Include mode in response
                        })

            return Response(slots)

        except Exception as e:
            logger.error(f"Error getting available slots: {str(e)}")
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def doctor_schedule(self, request):
        """
        Get a doctor's schedule for a specific date range.
        Query params: doctor_id, start_date, end_date
        """
        try:
            doctor_id = request.query_params.get('doctor_id')
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            
            if not all([doctor_id, start_date, end_date]):
                return Response(
                    {'error': 'doctor_id, start_date, and end_date are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            appointments = Appointment.objects.filter(
                doctor_id=doctor_id,
                start_time__date__range=[start_date, end_date],
                status__in=['pending', 'confirmed']
            )
            serializer = self.serializer_class(appointments, many=True)
            return Response(serializer.data)
        except Exception as e:
            logger.error(f"Error in getting doctor schedule: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def statistics(self, request):
        """
        Get appointment statistics for a date range.
        Query params: start_date, end_date
        """
        try:
            start_date = request.query_params.get('start_date')
            end_date = request.query_params.get('end_date')
            
            if not all([start_date, end_date]):
                return Response(
                    {'error': 'start_date and end_date are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            appointments = Appointment.objects.filter(
                start_time__date__range=[start_date, end_date]
            )

            stats = {
                'total': appointments.count(),
                'by_status': {
                    status: appointments.filter(status=status).count()
                    for status, _ in Appointment.STATUS_CHOICES
                },
                'by_type': {
                    type_: appointments.filter(appointment_type=type_).count()
                    for type_, _ in Appointment.TYPE_CHOICES
                }
            }

            return Response(stats)
        except Exception as e:
            logger.error(f"Error in getting statistics: {str(e)}")
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'])
    def available_slots(self, request):
        """
        Get available slots for a doctor on a specific date.
        This API is public and does not require authentication.
        
        Query parameters:
        - doctor_id: ID of the doctor (required)
        - date: Date in YYYY-MM-DD format (optional, if not provided, start_date and end_date are required)
        - start_date: Start date in YYYY-MM-DD format (required if date is not provided)
        - end_date: End date in YYYY-MM-DD format (required if date is not provided)
        - timerange: Time range in HH:MM-HH:MM format (optional, e.g. "09:00-17:00")
        - include_bookings: Whether to include booking information (default: true)
        - timezone: Timezone for time conversion (e.g., 'Asia/Ho_Chi_Minh')
        """
        doctor_id = request.query_params.get('doctor_id')
        date_str = request.query_params.get('date')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        timerange = request.query_params.get('timerange')
        include_bookings = request.query_params.get('include_bookings', 'true').lower() == 'true'
        timezone_str = request.query_params.get('timezone', 'UTC')

        if not doctor_id:
            return Response(
                {'error': 'doctor_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Either date or (start_date and end_date) must be provided
        if not date_str and not (start_date_str and end_date_str):
            return Response(
                {'error': 'Either date or both start_date and end_date are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # If date is provided, use it as both start and end date
            if date_str:
                date = datetime.strptime(date_str, '%Y-%m-%d').date()
                start_date = date
                end_date = date
            else:
                # Parse start_date and end_date
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            
            # Parse timerange if provided
            start_time = None
            end_time = None
            if timerange:
                try:
                    start_time_str, end_time_str = timerange.split('-')
                    start_time = datetime.strptime(start_time_str, '%H:%M').time()
                    end_time = datetime.strptime(end_time_str, '%H:%M').time()
                except ValueError:
                    return Response(
                        {'error': 'Invalid timerange format. Use HH:MM-HH:MM'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        except ValueError:
            return Response(
                {'error': 'Invalid date format. Use YYYY-MM-DD'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get all availabilities for the doctor
        availabilities = DoctorAvailability.objects.filter(
            doctor_id=doctor_id,
            is_active=True
        ).filter(
            models.Q(
                # Non-recurring availabilities
                recurrence_type='none',
                start_date__lte=end_date,
                end_date__gte=start_date
            ) |
            models.Q(
                # Recurring availabilities
                ~models.Q(recurrence_type='none'),
                start_date__lte=end_date
            )
        )

        # Get all occurrences for the date range
        all_occurrences = []
        for availability in availabilities:
            occurrences = availability.get_occurrences(start_date, end_date)
            for occurrence in occurrences:
                # Add availability metadata to each occurrence
                occurrence['availability_id'] = availability.id
                occurrence['title'] = availability.title or f"{availability.get_recurrence_type_display()} Availability"
                occurrence['clinic_id'] = availability.clinic_id
                occurrence['enterprise_id'] = availability.enterprise_id
                occurrence['recurrence_type'] = availability.recurrence_type
                occurrence['need_payment'] = availability.need_payment
                occurrence['mode'] = availability.mode
                all_occurrences.append(occurrence)

        # Sort by date and time
        all_occurrences.sort(key=lambda x: (x['date'], x['start_time']))

        # Get booked appointments if requested
        booked_appointments = {}
        if include_bookings:
            from appointments.models import Appointment
            from django.utils import timezone
            
            # Convert dates to timezone-aware datetimes for comparison
            start_datetime = timezone.make_aware(
                datetime.combine(start_date, datetime.min.time())
            )
            end_datetime = timezone.make_aware(
                datetime.combine(end_date, datetime.max.time())
            )
            
            # Get all appointments for the doctor in this date range
            appointments = Appointment.objects.filter(
                doctor_id=doctor_id,
                start_time__range=(start_datetime, end_datetime),
                status__in=['pending', 'confirmed']  # Only consider pending and confirmed appointments
            ).select_related('patient', 'clinic', 'enterprise')
            
            for appointment in appointments:
                date_str = appointment.start_time.date().strftime('%Y-%m-%d')
                if date_str not in booked_appointments:
                    booked_appointments[date_str] = []
                
                booked_appointments[date_str].append({
                    'id': appointment.id,
                    'start_time': appointment.start_time.strftime('%H:%M'),
                    'end_time': appointment.end_time.strftime('%H:%M'),
                    'status': appointment.status,
                    'patient_id': appointment.patient_id,
                    'patient_name': appointment.patient.get_full_name() if appointment.patient else None,
                    'title': appointment.title,
                    'clinic_id': appointment.clinic_id,
                    'enterprise_id': appointment.enterprise_id,
                    'appointment_type': appointment.appointment_type,
                    'mode': appointment.mode
                })

        # Group slots by date
        slots_by_date = {}
        for occurrence in all_occurrences:
            date_str = occurrence['date'].strftime('%Y-%m-%d')
            
            # Filter by timerange if provided
            if start_time and end_time:
                if occurrence['start_time'] < start_time or occurrence['end_time'] > end_time:
                    continue

            if date_str not in slots_by_date:
                slots_by_date[date_str] = {
                    'date': date_str,
                    'day_of_week': occurrence['date'].strftime('%A'),
                    'slots': [],
                    'total_slots': 0,
                    'available_slots': 0,
                    'booked_slots': 0
                }

            # Check if this slot is booked
            is_booked = False
            booking_info = None
            
            if include_bookings and date_str in booked_appointments:
                for booking in booked_appointments[date_str]:
                    booking_start = datetime.strptime(booking['start_time'], '%H:%M').time()
                    booking_end = datetime.strptime(booking['end_time'], '%H:%M').time()
                    
                    if (booking_start <= occurrence['end_time'] and 
                        booking_end >= occurrence['start_time']):
                        is_booked = True
                        booking_info = booking
                        break

            # Add slot with booking information
            slot = {
                'start_time': occurrence['start_time'].strftime('%H:%M'),
                'end_time': occurrence['end_time'].strftime('%H:%M'),
                'availability_id': occurrence['availability_id'],
                'title': occurrence['title'],
                'clinic_id': occurrence['clinic_id'],
                'enterprise_id': occurrence['enterprise_id'],
                'recurrence_type': occurrence['recurrence_type'],
                'mode': occurrence['mode'],
                'need_payment': occurrence['need_payment'],
                'status': 'booked' if is_booked else 'available',
                'booking_info': booking_info
            }
            
            slots_by_date[date_str]['slots'].append(slot)
            slots_by_date[date_str]['total_slots'] += 1
            if is_booked:
                slots_by_date[date_str]['booked_slots'] += 1
            else:
                slots_by_date[date_str]['available_slots'] += 1

        # Prepare response
        result = {
            'doctor_id': doctor_id,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'timezone': timezone_str,
            'total_days': len(slots_by_date),
            'total_slots': sum(day['total_slots'] for day in slots_by_date.values()),
            'total_available_slots': sum(day['available_slots'] for day in slots_by_date.values()),
            'total_booked_slots': sum(day['booked_slots'] for day in slots_by_date.values()),
            'schedule': []
        }
        
        # Convert dictionary to list and sort by date
        for date_str, day_data in sorted(slots_by_date.items()):
            result['schedule'].append(day_data)
            
        return Response(result) 